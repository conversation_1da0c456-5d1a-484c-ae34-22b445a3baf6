import { to<PERSON>ano } from '@ton/core';
import { useTonConnectUI } from '@tonconnect/ui-react';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

import { useRootContext } from '@/root-context';

export function useDepositValidation() {
  const { appConfig } = useRootContext();

  const validateAmount = useCallback(
    (amount: string): boolean => {
      if (!amount || !appConfig) return false;

      const numAmount = parseFloat(amount);
      if (isNaN(numAmount) || numAmount <= 0) return false;

      return numAmount >= appConfig.min_deposit_amount;
    },
    [appConfig],
  );

  return { validateAmount, appConfig };
}

export function useDepositTransaction() {
  const [tonConnectUI] = useTonConnectUI();
  const { appConfig, refetchUser } = useRootContext();
  const [loading, setLoading] = useState(false);

  const executeDeposit = useCallback(
    async (
      amount: string,
    ): Promise<{ success: boolean; transactionHash?: string }> => {
      if (!appConfig) {
        toast.error('Configuration not loaded');
        return { success: false };
      }

      if (!tonConnectUI.connected || !tonConnectUI.account?.address) {
        toast.error('Please connect your wallet first');
        return { success: false };
      }

      const marketplaceWallet =
        process.env.NEXT_PUBLIC_MARKETPLACE_WALLET_ADDRESS;
      if (!marketplaceWallet) {
        toast.error('Marketplace wallet not configured');
        return { success: false };
      }

      try {
        setLoading(true);

        const numAmount = parseFloat(amount);
        const totalAmount = numAmount + appConfig.deposit_fee;

        const transaction = {
          validUntil: Math.floor(Date.now() / 1000) + 300, // 5 minutes
          messages: [
            {
              address: marketplaceWallet,
              amount: toNano(totalAmount.toString()).toString(),
            },
          ],
        };

        const result = await tonConnectUI.sendTransaction(transaction);

        // For now, we'll generate a mock transaction hash since the actual hash
        // extraction from TON Connect UI requires more complex BOC parsing
        // In a real implementation, you would parse the BOC to get the actual hash
        const transactionHash = result?.boc
          ? `${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
          : undefined;

        toast.success(`Deposit of ${numAmount} TON initiated successfully!`);
        return { success: true, transactionHash };
      } catch (error) {
        console.error('Deposit failed:', error);
        toast.error('Deposit failed. Please try again.');
        return { success: false };
      } finally {
        setLoading(false);
      }
    },
    [tonConnectUI, appConfig],
  );

  const refetchUserData = useCallback(
    async ({
      successToastMessage = 'Balance updated successfully!',
    }: { successToastMessage?: string } = {}) => {
      try {
        await refetchUser();
        if (successToastMessage) {
          toast.success(successToastMessage);
        }
      } catch (error) {
        console.error('Failed to refetch user:', error);
        toast.error('Failed to update balance');
      }
    },
    [refetchUser],
  );

  return {
    executeDeposit,
    refetchUserData,
    loading,
    isWalletConnected: !!tonConnectUI.account?.address,
  };
}

export function useDepositState() {
  const [depositAmount, setDepositAmount] = useState('');
  const [showDepositModal, setShowDepositModal] = useState(false);
  const [transactionHash, setTransactionHash] = useState<undefined | string>();
  const [isDepositSuccess, setIsDepositSuccess] = useState(false);

  const resetForm = useCallback(() => {
    setDepositAmount('');
    setTransactionHash(undefined);
    setIsDepositSuccess(false);
  }, []);

  const openDepositModal = useCallback(() => {
    setShowDepositModal(true);
    setIsDepositSuccess(false);
  }, []);

  const closeDepositModal = useCallback(() => {
    setShowDepositModal(false);
    setTransactionHash(undefined);
    setIsDepositSuccess(false);
  }, []);

  const setDepositSuccess = useCallback((txHash?: string) => {
    setIsDepositSuccess(true);
    if (txHash) {
      setTransactionHash(txHash);
    }
  }, []);

  return {
    depositAmount,
    setDepositAmount,
    showDepositModal,
    transactionHash,
    isDepositSuccess,
    resetForm,
    openDepositModal,
    closeDepositModal,
    setDepositSuccess,
    // Legacy support for existing CountdownPopup
    showCountdownPopup: showDepositModal,
    openCountdownPopup: openDepositModal,
    closeCountdownPopup: closeDepositModal,
  };
}
