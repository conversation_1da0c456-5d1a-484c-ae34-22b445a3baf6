'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { useTonConnectUI } from '@tonconnect/ui-react';
import { httpsCallable } from 'firebase/functions';
import { AlertTriangle, ArrowDown } from 'lucide-react';
import { useState } from 'react';
import { useIntl } from 'react-intl';
import { toast } from 'sonner';
import { Drawer } from 'vaul';

import { formatServerError } from '@/api/server-error-handler';
import { TonLogo } from '@/components/TonLogo';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useWithdrawalStatus } from '@/components/withdraw-drawer/use-withdrawal-status';
import { useVisualViewport } from '@/hooks/use-visual-viewport';
import { firebaseFunctions, useRootContext } from '@/root-context';

import { withdrawDrawerMessages } from '../intl/withdraw-drawer.messages';

interface WithdrawDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface WithdrawResponse {
  success: boolean;
  message: string;
  netAmount: number;
  feeAmount: number;
  transactionHash: string;
}

export function WithdrawDrawer({ open, onOpenChange }: WithdrawDrawerProps) {
  const { formatMessage: t } = useIntl();
  const [tonConnectUI] = useTonConnectUI();
  const { currentUser, appConfig, refetchUser } = useRootContext();
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const {
    withdrawalStatus,
    loading: statusLoading,
    refetch: refetchStatus,
  } = useWithdrawalStatus(
    currentUser,
    appConfig?.max_withdrawal_amount ?? Number.MAX_SAFE_INTEGER,
  );
  const drawerContentRef = useVisualViewport({ enabled: open, offset: 64 });

  const validateAmount = (amount: string): boolean => {
    if (!amount || !appConfig || !currentUser?.balance || !withdrawalStatus)
      return false;

    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) return false;

    if (numAmount < 1) return false;

    const availableBalance =
      currentUser.balance.sum - currentUser.balance.locked;
    if (numAmount > availableBalance) return false;

    if (numAmount > withdrawalStatus.remainingLimit) return false;

    return true;
  };

  const isValidAmount = validateAmount(withdrawAmount);
  const availableBalance = currentUser?.balance
    ? currentUser.balance.sum - currentUser.balance.locked
    : 0;

  const handleWithdraw = async () => {
    if (!isValidAmount || !appConfig) {
      toast.error(t(withdrawDrawerMessages.invalidWithdrawalAmount));
      return;
    }

    if (!tonConnectUI.account?.address) {
      toast.error(t(withdrawDrawerMessages.pleaseConnectWalletFirst));
      return;
    }

    if (!currentUser?.ton_wallet_address) {
      toast.error(t(withdrawDrawerMessages.noWalletAddressFound));
      return;
    }

    try {
      setLoading(true);

      const amount = parseFloat(withdrawAmount);

      const withdrawFundsFunction = httpsCallable<
        { amount: number },
        WithdrawResponse
      >(firebaseFunctions, 'withdrawFunds');

      const result = await withdrawFundsFunction({ amount });

      console.log('Withdrawal result:', result.data);
      toast.success(
        t(withdrawDrawerMessages.withdrawalSuccessful, {
          hash: result.data.transactionHash,
        }),
      );

      setWithdrawAmount('');
      onOpenChange(false);

      await Promise.all([refetchUser(), refetchStatus()]);
    } catch (error) {
      console.error('Withdrawal failed:', error);
      const errorMessage = formatServerError(error, t);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setWithdrawAmount('');
    onOpenChange(false);
  };

  const getWithdrawFee = () => {
    if (!appConfig || !withdrawAmount) return 0;
    const amount = parseFloat(withdrawAmount);
    if (isNaN(amount)) return 0;
    return appConfig.withdrawal_fee;
  };

  const getNetAmount = () => {
    if (!withdrawAmount) return 0;
    const amount = parseFloat(withdrawAmount);
    if (isNaN(amount)) return 0;
    return Math.max(0, amount - getWithdrawFee());
  };

  return (
    <Drawer.Root
      open={open}
      onOpenChange={onOpenChange}
      shouldScaleBackground
      modal={true}
      dismissible={true}
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content
          ref={drawerContentRef}
          className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none max-h-[90vh]"
        >
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="max-w-md mx-auto space-y-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-[#ec3942] rounded-full flex items-center justify-center">
                  <ArrowDown className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-[#f5f5f5]">
                    {t(withdrawDrawerMessages.withdrawFunds)}
                  </h2>
                  <Caption level="2" weight="3" className="text-[#708499]">
                    {t(withdrawDrawerMessages.withdrawTonToWallet)}
                  </Caption>
                </div>
              </div>

              {!appConfig ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6ab2f2] mx-auto"></div>
                  <Caption level="2" weight="3" className="text-[#708499] mt-2">
                    {t(withdrawDrawerMessages.loadingConfiguration)}
                  </Caption>
                </div>
              ) : (
                <>
                  <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
                    <div className="w-full flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-[#ec3942] flex-shrink-0 mt-0.5" />
                      <div className="w-full text-sm">
                        <p className="w-full font-medium text-[#f5f5f5] mb-2">
                          {t(withdrawDrawerMessages.withdrawalInformation)}
                        </p>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center py-1">
                            <span className="text-[#708499]">
                              {t(withdrawDrawerMessages.minimumWithdrawal)}
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                1
                              </span>
                              <TonLogo size={24} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center py-1">
                            <span className="text-[#708499]">
                              {t(withdrawDrawerMessages.withdrawalFee)}
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {appConfig?.withdrawal_fee || 0.1}
                              </span>
                              <TonLogo size={24} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center py-1">
                            <span className="text-[#708499]">
                              {t(withdrawDrawerMessages.availableBalance)}
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {availableBalance.toFixed(2)}
                              </span>
                              <TonLogo size={24} />
                            </div>
                          </div>
                          {withdrawalStatus && (
                            <>
                              <div className="flex justify-between items-center py-1">
                                <span className="text-[#708499]">
                                  {t(withdrawDrawerMessages.withdrawalLimit24h)}
                                </span>
                                <div className="flex items-center gap-1">
                                  <span className="text-[#6ab2f2] font-semibold">
                                    {withdrawalStatus.maxLimit}
                                  </span>
                                  <TonLogo size={24} />
                                </div>
                              </div>
                              <div className="flex justify-between items-center py-1">
                                <span className="text-[#708499]">
                                  {t(withdrawDrawerMessages.remainingLimit)}
                                </span>
                                <div className="flex items-center gap-1">
                                  <span className="text-[#6ab2f2] font-semibold">
                                    {withdrawalStatus.remainingLimit.toFixed(2)}
                                  </span>
                                  <TonLogo size={24} />
                                </div>
                              </div>
                              <div className="flex justify-between items-center py-1">
                                <span className="text-[#708499]">
                                  {t(withdrawDrawerMessages.limitResetsAt)}
                                </span>
                                <span className="text-[#6ab2f2] font-semibold text-xs">
                                  {new Date(
                                    withdrawalStatus.resetAt,
                                  ).toLocaleString()}
                                </span>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label
                          htmlFor="withdraw-amount"
                          className="text-sm font-medium text-[#f5f5f5]"
                        >
                          {t(withdrawDrawerMessages.withdrawAmountTon)}
                        </Label>
                        <Button
                          onClick={() =>
                            setWithdrawAmount(
                              Math.min(
                                availableBalance,
                                withdrawalStatus?.remainingLimit ??
                                  availableBalance,
                              ).toString(),
                            )
                          }
                        >
                          {t(withdrawDrawerMessages.max)}
                        </Button>
                      </div>
                      <Input
                        id="withdraw-amount"
                        type="number"
                        step="0.01"
                        min="1"
                        max={
                          withdrawalStatus
                            ? Math.min(
                                availableBalance,
                                withdrawalStatus.remainingLimit,
                              )
                            : availableBalance
                        }
                        placeholder={t(
                          withdrawDrawerMessages.enterAmountToWithdraw,
                        )}
                        value={withdrawAmount}
                        onChange={(e) => setWithdrawAmount(e.target.value)}
                        className="mt-2 bg-[#232e3c]/50 border-[#3a4a5c]/50 text-[#f5f5f5] placeholder:text-[#708499] focus:border-[#6ab2f2] focus:ring-[#6ab2f2]/20"
                        disabled={loading || statusLoading}
                      />
                      {withdrawAmount && (
                        <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30 mt-3">
                          <div className="space-y-3">
                            <div className="flex justify-between items-center">
                              <span className="text-[#708499]">
                                {t(withdrawDrawerMessages.withdrawAmount)}
                              </span>
                              <div className="flex items-center gap-1">
                                <span className="text-[#6ab2f2] font-semibold">
                                  {parseFloat(withdrawAmount).toFixed(2)}
                                </span>
                                <TonLogo size={24} />
                              </div>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-[#708499]">
                                {t(withdrawDrawerMessages.withdrawalFee)}
                              </span>
                              <div className="flex items-center gap-1">
                                <span className="text-[#ec3942] font-semibold">
                                  -{getWithdrawFee().toFixed(2)}
                                </span>
                                <TonLogo size={24} />
                              </div>
                            </div>
                            <div className="flex justify-between items-center font-medium border-t border-[#3a4a5c]/30 pt-3">
                              <span className="text-[#f5f5f5]">
                                {t(withdrawDrawerMessages.youWillReceive)}
                              </span>
                              <div className="flex items-center gap-1">
                                <span className="text-[#6ab2f2] font-semibold">
                                  {getNetAmount().toFixed(2)}
                                </span>
                                <TonLogo size={24} />
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>

                    {withdrawAmount && !isValidAmount && (
                      <div className="text-center">
                        <Caption
                          level="2"
                          weight="3"
                          className="text-[#ec3942]"
                        >
                          {parseFloat(withdrawAmount) < 1
                            ? t(withdrawDrawerMessages.minimumWithdrawalAmount)
                            : parseFloat(withdrawAmount) > availableBalance
                              ? t(
                                  withdrawDrawerMessages.insufficientAvailableBalance,
                                )
                              : withdrawalStatus &&
                                  parseFloat(withdrawAmount) >
                                    withdrawalStatus.remainingLimit
                                ? `Exceeds 24-hour limit. Remaining: ${withdrawalStatus.remainingLimit.toFixed(2)} TON`
                                : t(withdrawDrawerMessages.invalidAmount)}
                        </Caption>
                      </div>
                    )}

                    <div className="space-y-3 pt-4">
                      <Button
                        onClick={handleWithdraw}
                        disabled={
                          !isValidAmount ||
                          loading ||
                          statusLoading ||
                          !tonConnectUI.account?.address ||
                          !withdrawalStatus
                        }
                        className="w-full h-12 bg-[#ec3942] hover:bg-[#ec3942]/90 text-white border-0 rounded-2xl"
                      >
                        {loading ? (
                          t(withdrawDrawerMessages.processing)
                        ) : (
                          <>
                            {t(withdrawDrawerMessages.withdraw)}{' '}
                            {withdrawAmount && isValidAmount ? (
                              <>
                                &#40;{getNetAmount().toFixed(2)}{' '}
                                <TonLogo className="-m-2" size={24} />
                                <span className="-ml-1 translate-x-[1px]">
                                  &#41;
                                </span>
                              </>
                            ) : (
                              ''
                            )}
                          </>
                        )}
                      </Button>

                      <Button
                        variant="outline"
                        onClick={handleClose}
                        className="w-full h-12 bg-transparent border-[#3a4a5c]/50 text-[#708499] hover:bg-[#232e3c]/50 hover:text-[#f5f5f5] rounded-2xl"
                        disabled={loading}
                      >
                        {t(withdrawDrawerMessages.cancel)}
                      </Button>
                    </div>

                    {!tonConnectUI.account?.address && (
                      <div className="text-center mt-4">
                        <Caption
                          level="2"
                          weight="3"
                          className="text-[#ec3942]"
                        >
                          {t(withdrawDrawerMessages.pleaseConnectWallet)}
                        </Caption>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
