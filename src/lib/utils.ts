import { type ClassValue, clsx } from 'clsx';
import { Timestamp } from 'firebase/firestore';
import { twMerge } from 'tailwind-merge';

import { CDN_BASE_URL } from '@/core.constants';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Date utilities (from date-utils.ts)
export const firebaseTimestampToDate = (timestamp: Timestamp | Date): Date => {
  if (timestamp instanceof Date) {
    return timestamp;
  }

  return timestamp.toDate();
};

export const formatDateToFirebaseTimestamp = (
  date: Date | Timestamp,
): Timestamp => {
  if (date instanceof Timestamp) {
    return date;
  }
  return Timestamp.fromDate(date);
};

// Image path utilities (from image-path.ts)
export const getImagePathWithFallback = (
  collectionId: string,
  format: 'png' | 'tgs' = 'png',
) => {
  if (!collectionId) return { primary: '', fallback: '' };

  const cdnUrl = `${CDN_BASE_URL}/${collectionId}/Original.${format}`;
  const localPath = `/limited/${collectionId}/Original.${format}`;

  return {
    primary: cdnUrl,
    fallback: localPath,
  };
};

// Location utilities (from location.ts)
export function isUserPidor(): boolean {
  if (typeof window === 'undefined') {
    return false; // SSR safety
  }

  const locale = navigator.language || navigator.languages?.[0] || '';

  return locale.toLowerCase().startsWith('ru');
}

// Math utilities (from math-utils.ts)
export function roundToThreeDecimals(value?: number | null) {
  if (value === null || value === undefined) {
    return 0;
  }

  return Math.round(value * 1000) / 1000;
}

export function safeMultiply(amount: number, multiplier: number) {
  return roundToThreeDecimals(amount * multiplier);
}
